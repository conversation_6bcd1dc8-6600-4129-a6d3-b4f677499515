'server-only';

import { NextAuthOptions, getServerSession } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse
import { refreshUserDataForJWT } from '@/actions/user.action';
import { autoCreateSchoolForUser } from '@/actions/school.action';
import { handleGetSubscriptionStatusAction } from '@/actions/package.action';
import { EUserRole } from '@/config/enums/user';

const secret = process.env.AUTH_SECRET;
const URL = process.env.API_URL;
/**
 * Takes a token, and returns a new token with updated
 * `accessToken` and `accessTokenExpires`. If an error occurs,
 * returns the old token and an error property
 */
async function refreshAccessToken(token: any): Promise<any> {
  try {
    const endpoint = `${URL}/auth/token/refresh`;
    const response = await fetch(endpoint, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token?.refreshToken || ''}`,
      },
    });

    if (!response.ok) return Promise.resolve({ ...token, isLogin: false });
    const data = await response.json();
    if (!response.ok) {
      throw data;
    }

    return {
      ...token,
      accessToken: data?.accessToken,
      refreshToken: data?.refreshToken,
    };
  } catch (error) {
    return {
      ...token,
      isLogin: false,
      error: 'RefreshAccessTokenError',
    };
  }
}

export const authOptions: NextAuthOptions = {
  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'email',
        },
        password: { label: 'Password', type: 'password' },
      },
      // @ts-expect-error - a bug in next-auth
      async authorize(credentials) {
        try {
          const endpoint = `${URL}/auth/sign-in`;

          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials?.email,
              password: credentials?.password,
            }),
          });
          const result = await response.json();

          if (response.ok) {
            // Extract user data directly from the sign-in response
            const userData = result?.data?.user || {};

            // Check if school data exists
            const hasSchoolData = userData.school && Object.keys(userData.school).length > 0;
            const schoolData = hasSchoolData ? userData.school : null;
            // Only extract brand data if school data exists
            const brandData = schoolData?.brand || {};

            const user = {
              id: userData.id || '',
              name: userData.name || '',
              email: userData.email || '',
              role: userData.role || '',
              schoolId: userData.schoolId || null,
              // Add token information
              accessToken: result?.data?.accessToken,
              refreshToken: result?.data?.refreshToken,
              accessTokenExpires: result?.data?.accessTokenExpires || Date.now() + 24 * 60 * 60 * 1000, // Default 24 hours
              isLogin: true,
              // Add school information only if school data exists
              school: hasSchoolData ? {
                id: schoolData.id || '',
                name: schoolData.name || '',
                address: schoolData.address || '',
                phoneNumber: schoolData.phoneNumber || '',
                registeredNumber: schoolData.registeredNumber || '',
                email: schoolData.email || '',
                brand: {
                  id: brandData.id || '',
                  logo: brandData.logo,
                  color: brandData.color || '',
                  image: brandData.image
                }
              } : undefined,
            };

            return Promise.resolve(user);
          }
          return Promise.resolve({ isLogin: false });
        } catch (error) {
          return Promise.resolve({ isLogin: false });
        }
      },
    }),
  ],

  callbacks: {
    async signIn({ user }) {
      // Check if user login was successful - focus purely on authentication
      return user?.isLogin || false;
    },
    async redirect({ url, baseUrl }) {
      console.log('[AUTH DEBUG] Redirect callback - url:', url, 'baseUrl:', baseUrl);
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      if (url.startsWith(baseUrl)) return url;
      return baseUrl + "/";
    },
    async session({ session, token }) {
      // Create a properly structured session with user information from the token
      const sessionData = {
        ...session,
        user: {
          id: token.id as string,
          name: token.name as string,
          email: token.email as string,
          accessToken: token.accessToken as string,
          role: token.role as string,
          schoolId: token.schoolId as string | null,
          isLogin: token.isLogin as boolean,
          school: token.school,
          subscription: token.subscription,
        },
      };

      return sessionData;
    },
    async jwt({ token, user, account, trigger, session: newSessionData }) {
      // 1. Initial sign-in: Copy all relevant user details to the token
      if (account && user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.role = user.role;
        token.schoolId = user.schoolId;
        token.isLogin = user.isLogin;
        token.accessToken = user.accessToken;
        token.refreshToken = (user as any).refreshToken;
        token.accessTokenExpires = (user as any).accessTokenExpires;
        token.school = user.school as ISchoolResponse | undefined;
        
        console.log('[JWT Callback] User Role:', user.role);
        // If INDEPENDENT_TEACHER or SCHOOL_MANAGER, fetch subscription status
        if (user.role === EUserRole.INDEPENDENT_TEACHER || user.role === EUserRole.SCHOOL_MANAGER) {
          try {
            console.log('[JWT Callback] Fetching subscription for user ID:', user.id);
            const subscriptionResult = await handleGetSubscriptionStatusAction(user.accessToken);
            console.log('[JWT Callback] Subscription Result:', subscriptionResult);
            if (subscriptionResult.status === 'success' && subscriptionResult.data) {
              token.subscription = subscriptionResult.data;
              console.log('[JWT Callback] Subscription data added to token:', token.subscription);
            }
          } catch (error) {
            console.error(`[JWT Callback] Error fetching subscription for: ${user.email}`, error);
          }
        }

        // If INDEPENDENT_TEACHER, check for existing school and create if none
        if (user.role === EUserRole.INDEPENDENT_TEACHER) {

          // Check if user already has a school
          if (!user.schoolId && user.id && user.name && user.email) {
            try {
              const schoolCreationResult = await autoCreateSchoolForUser({
                id: user.id,
                name: user.name,
                email: user.email,
              });
              if (schoolCreationResult.status === 'success' && schoolCreationResult.data) {
                token.schoolId = schoolCreationResult.data.id;
                token.school = schoolCreationResult.data;
              } else {
                const errorMessage = schoolCreationResult.status === 'error' ? schoolCreationResult.message : 'Unknown error';
                console.warn(`[JWT Callback] Failed to create school for: ${user.email}, error: ${errorMessage}`);
              }
            } catch (error) {
              console.error(`[JWT Callback] Error creating school for: ${user.email}`, error);
            }
          }
        }
      }

      console.log('[JWT Callback] Token after initial sign-in:', token);

      // 2. Handle session updates via useSession().update()
      if (trigger === "update" && newSessionData?.user) {
        // Update user profile data if provided
        if (newSessionData.user.name !== undefined) {
          token.name = newSessionData.user.name;
        }

        if (newSessionData.user.email !== undefined) {
          token.email = newSessionData.user.email;
        }

        // Update schoolId if provided
        if (newSessionData.user.schoolId !== undefined) {
          token.schoolId = newSessionData.user.schoolId;
        }

        // Update school data if provided
        if (newSessionData.user.school) {
          if (token.school && typeof token.school === 'object') {
            token.school = {
              ...(token.school as ISchoolResponse),
              ...newSessionData.user.school,
            };
          } else {
            token.school = newSessionData.user.school as ISchoolResponse;
          }

          // Also update schoolId from school.id if not explicitly provided
          if (newSessionData.user.schoolId === undefined && newSessionData.user.school.id) {
            token.schoolId = newSessionData.user.school.id;
          }
        }

        // Handle legacy school name update (for backward compatibility)
        if (newSessionData.user.schoolName && token.schoolId && token.id) {
          if (token.school && typeof token.school === 'object') {
            token.school = {
              ...(token.school as ISchoolResponse),
              name: newSessionData.user.schoolName,
            };
          } else if (!token.school) {
            token.school = {
              id: token.schoolId,
              name: newSessionData.user.schoolName,
              address: '',
              phoneNumber: '',
              registeredNumber: '',
              email: '',
            } as ISchoolResponse;
          }
        }
      }

      // 4. Check if access token needs refresh
      if (token.accessTokenExpires && Date.now() > (token.accessTokenExpires as number)) {
        console.log('[JWT Callback] Access token expired, attempting refresh...');
        return refreshAccessToken(token);
      }

      return token;
    },
  },
  pages: {
    signIn: `/auth/sign-in`,
    error: `/auth/sign-in?type=error`,
  },
  secret,
  debug: true,
};

export const onSeverSession = () => {
  const session = getServerSession(authOptions);
  return session;
};
