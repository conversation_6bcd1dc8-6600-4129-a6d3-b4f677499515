'use client';

import * as React from 'react';
import { PricingCard } from '@/components/molecules/PricingCard';
import { PricingToggle } from '@/components/atoms/PricingToggle/PricingToggle';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

const plans = [
  {
    name: 'Basic',
    description: 'Perfect for small teams getting started',
    monthlyPrice: 10,
    yearlyPrice: 100,
    features: ['5 Users', '10GB Storage', 'Email Support'],
  },
  {
    name: 'Pro',
    description: 'Ideal for growing teams and businesses',
    monthlyPrice: 25,
    yearlyPrice: 250,
    features: ['20 Users', '50GB Storage', 'Priority Email Support'],
    popular: true,
  },
  {
    name: 'Enterprise',
    description: 'Advanced features for large organizations',
    monthlyPrice: 50,
    yearlyPrice: 500,
    features: ['Unlimited Users', '200GB Storage', 'Phone & Email Support'],
  },
];

interface PricingPlanSelectorProps {
  isManagement?: boolean;
}

export const PricingPlanSelector: React.FC<PricingPlanSelectorProps> = ({ isManagement = false }) => {
  const [isYearly, setIsYearly] = React.useState(false);
  const { data: session } = useSession();
  console.log('session',session?.user);
  const router = useRouter();
  const userPlanName = session?.user.subscription?.package?.name;

  const handleManageSubscription = () => {
    // Redirect to a subscription management portal, e.g., Stripe Customer Portal
    // This is a placeholder URL.
    router.push('/api/stripe/create-portal-session');
  };

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-8 sm:mb-12">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-text-primary mb-4">
          {isManagement ? 'My Subscription' : 'Our Pricing'}
        </h2>
      </div>

      {/* Toggle */}
      <div className="flex justify-center mb-8 sm:mb-12">
        <PricingToggle onToggle={setIsYearly} />
      </div>

      {/* Pricing Cards Grid - Optimized for tablet */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-5 md:gap-6 lg:gap-8 items-stretch justify-items-center max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <div key={index} className="w-full max-w-sm sm:max-w-none md:max-w-sm lg:max-w-none">
            <PricingCard
              package={{
                name: plan.name,
                description: plan.description,
                price: isYearly ? plan.yearlyPrice : plan.monthlyPrice,
                features: plan.features,
                currency: 'USD',
                interval: isYearly ? 'year' : 'month',
                popular: plan.popular,
              }}
              onSubscribe={() => {
                console.log(`Subscribe to ${plan.name}`);
              }}
              onManageSubscription={handleManageSubscription}
              isCurrentPlan={isManagement && userPlanName === plan.name}
              className="w-full h-full"
            />
          </div>
        ))}
      </div>
    </div>
  );
};