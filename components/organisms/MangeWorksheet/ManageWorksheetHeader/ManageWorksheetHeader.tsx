'use client';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { useSession } from 'next-auth/react';

export const ManageWorksheetHeader: React.FC = () => {
  const { data: session } = useSession();
  console.log('session',session?.user);
  const hasSubscription = session?.user.subscription?.status === 'active';

  return (
    <ListingHeader
      title={'Manage Worksheet'}
      buttonProps={[
        {
          iconProps: { size: 3.5, variant: 'hand-coins' },
          label: hasSubscription ? 'My Subscription' : 'Subscribe',
          href: hasSubscription ? '/subscription/me' : '/pricing',
          variant: 'outline',
          className:
            'w-fit border-text-secondary text-text-secondary hover:bg-text-secondary hover:text-background-default transition-all duration-200 ease-in-out',
        },
        {
          iconProps: { size: 3.5, variant: 'plus' },
          label: 'Create worksheet',
          href: '/manage-worksheet?type=create',
          className: 'w-fit text-white',
        },
      ]}
    />
  );
};
